import { SNSClient, PublishCommand } from '@aws-sdk/client-sns';
import { ISmsClient } from './ISmsClient';
import { Result2 } from '@shared/core/Result2';
import { AWSSNSClientErrors } from './AWSSNSClientErrors';
import { PossibleLngs } from '@shared/utils/utils';
import { IContextProvider } from '@shared/context/IContextProvider';
import * as console from 'node:console';

/**
 * AWS SNS implementation of the ISmsClient interface
 */
export class AWSSNSClient implements ISmsClient {
  private readonly client: SNSClient;
  private readonly contextProvider: IContextProvider;

  public constructor(args: { contextProvider: IContextProvider }) {
    this.client = new SNSClient({});
    this.contextProvider = args.contextProvider;
  }

  /**
   * Sends an SMS message using AWS SNS
   * @param params - The parameters for the SMS message
   * @returns A Result2 that resolves to undefined on success or errors on failure
   */
  public async send(params: { 
    msg: string; 
    phoneNumber: string;
    lng: PossibleLngs;
  }): Promise<Result2<undefined>> {
    const { msg, phoneNumber, lng } = params;
    try {
      const { MessageId } = await this.client.send(new PublishCommand({
        Message: msg,
        PhoneNumber: phoneNumber,
      }));
      console.log(`Message sent successfully with ID: ${MessageId}`);
      return Result2.ok(undefined);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      // Log all errors for monitoring
      console.error('AWS SNS error:', error);
      const { requestId, cfId, extendedRequestId } = error.$metadata;
      console.log({ requestId, cfId, extendedRequestId });
      
      // Send to analytics for monitoring
      await this.contextProvider.sendAnalytics({ 
        error: error.name || 'Unknown', 
        errorMessage: error.message || 'Unknown error',
        phoneNumber,
        metadata: error.$metadata,
      });

      // TODO InvalidParameter en lugar de InvalidParameterException?
      // Handle specific AWS SNS errors https://docs.aws.amazon.com/sns/latest/api/API_Publish.html#API_Publish_Errors
      if (error.name === 'InvalidParameterException' && 
          error.message.includes('phone')) {
        return Result2.fail([new AWSSNSClientErrors.InvalidPhoneNumber({ lng })]);
      } else if (error.name === 'EndpointDisabled') {
        return Result2.fail([new AWSSNSClientErrors.PhoneNumberOptedOut({ lng })]);
      }
      
      // For all other errors, return a generic internal error
      return Result2.fail([new AWSSNSClientErrors.InternalError({ lng })]);
    }
  }
}