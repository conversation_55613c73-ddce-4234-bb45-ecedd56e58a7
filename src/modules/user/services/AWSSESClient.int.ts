// Set USE_REAL_AWS=true and TEST_EMAIL_ADDRESS=<real email> in .env file to run this test
import { test, expect, vi, beforeEach, describe } from 'vitest';
import { AWSSESClient } from './AWSSESClient';
import { AWSSESClientErrors } from './AWSSESClientErrors';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { pickLng } from '@shared/utils/test';

const lng = pickLng();

// Skip tests if USE_REAL_AWS is not set to true
const USE_REAL_AWS = process.env.USE_REAL_AWS === 'true';
if (!USE_REAL_AWS) {
  test.skip('Integration tests skipped - set USE_REAL_AWS=true to run', () => {
    console.log('Skipping AWS SES integration tests. Set USE_REAL_AWS=true to run them.');
  });
} else {
  describe('AWSSESClient Integration Tests', () => {
    let sesClient: AWSSESClient;
    let contextProvider: ContextProvider;
    
    beforeEach(() => {
      vi.clearAllMocks();
      
      // Set required environment variables for integration tests
      if (!process.env.sesFromEmail) {
        vi.stubEnv('sesFromEmail', '<EMAIL>'); // Replace with your verified SES email
      }
      
      contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
      sesClient = new AWSSESClient({ contextProvider });
      vi.spyOn(contextProvider, 'sendAnalytics').mockResolvedValue();
    });

    test('client initializes without errors', () => {
      expect(sesClient).toBeInstanceOf(AWSSESClient);
    });

    test.only('sends a real email message', async () => {
      // IMPORTANT: This test will actually send an email and incur AWS charges
      // Use a valid email address that you can access
      const TEST_EMAIL_ADDRESS = process.env.TEST_EMAIL_ADDRESS;
      
      if (!TEST_EMAIL_ADDRESS)
        throw 'TEST_EMAIL_ADDRESS environment variable not set';

      const params = {
        msg: 'This is a test message from AWSSESClient integration test. Your verification code is: 1234',
        emailAddress: TEST_EMAIL_ADDRESS,
        subject: 'Test Verification Code',
        lng,
      };

      const result = await sesClient.send(params);
      
      // Check if the message was sent successfully
      expect(result.isSuccess).toBe(true);
    });

    test('handles invalid email address with real AWS', async () => {
      const params = {
        msg: 'Test message',
        emailAddress: 'clearly-invalid-email-format',
        subject: 'Test Subject',
        lng,
      };

      const result = await sesClient.send(params);
      
      // Should fail with invalid email address error
      expect(result.isFailure).toBe(true);
      expect(result.errors?.[0]).toBeInstanceOf(AWSSESClientErrors.InvalidEmailAddress);
    });

    test('handles bounced email address with real AWS', async () => {
      // Use the SES simulator email for bounce testing
      const params = {
        msg: 'Test message',
        emailAddress: '<EMAIL>',
        subject: 'Test Subject',
        lng,
      };

      const result = await sesClient.send(params);
      
      // Should fail with bounced email error
      expect(result.isFailure).toBe(true);
      expect(result.errors?.[0]).toBeInstanceOf(AWSSESClientErrors.EmailAddressBounced);
    });
  });
}
