import { test, expect, vi, beforeEach, describe } from 'vitest';
import { EmailSrv } from './EmailSrv';
import { IEmailClient } from './IEmailClient';
import { Result2 } from '@shared/core/Result2';
import { pickLng } from '@shared/utils/test';

const lng = pickLng();

describe('EmailSrv Unit Tests', () => {
  let emailSrv: EmailSrv;
  let mockEmailClient: IEmailClient;

  beforeEach(() => {
    // Create mock email client
    mockEmailClient = {
      send: vi.fn(),
    };
    
    emailSrv = new EmailSrv({ emailClient: mockEmailClient });
  });

  test('service initializes without errors', () => {
    expect(emailSrv).toBeInstanceOf(EmailSrv);
  });

  test.each([
    {
      description: 'English',
      lng: 'en' as const,
      code: 1234,
      expectedMsg: 'Your verification code is: 1234',
      expectedSubject: 'Verification Code',
    },
    {
      description: 'Spanish',
      lng: 'es' as const,
      code: 5678,
      expectedMsg: 'Tu código de verificación es: 5678',
      expectedSubject: 'Código de verificación',
    },
    {
      description: 'unknown language (defaults to English)',
      lng: 'fr' as any,
      code: 1234,
      expectedMsg: 'Your verification code is: 1234',
      expectedSubject: 'Verification Code',
    },
  ])('successfully sends verification code in $description', async ({ lng, code, expectedMsg, expectedSubject }) => {
    // Mock successful email client response
    vi.mocked(mockEmailClient.send).mockResolvedValueOnce(Result2.ok(undefined));

    const params = {
      emailAddress: '<EMAIL>',
      code,
      lng,
    };

    const result = await emailSrv.sendCode(params);

    // Verify result
    expect(result.isSuccess).toBe(true);

    // Verify email client was called with correct parameters
    expect(mockEmailClient.send).toHaveBeenCalledTimes(1);
    expect(mockEmailClient.send).toHaveBeenCalledWith({
      msg: expectedMsg,
      emailAddress: '<EMAIL>',
      subject: expectedSubject,
      lng,
    });
  });

  test('handles email client failure', async () => {
    // Mock email client failure
    const mockError = new Error('Email sending failed');
    vi.mocked(mockEmailClient.send).mockResolvedValueOnce(Result2.fail([mockError as any]));

    const params = {
      emailAddress: '<EMAIL>',
      code: 1234,
      lng,
    };

    const result = await emailSrv.sendCode(params);

    // Verify result
    expect(result.isFailure).toBe(true);
    expect(result.errors).toEqual([mockError]);

    // Verify email client was called
    expect(mockEmailClient.send).toHaveBeenCalledTimes(1);
  });
});
