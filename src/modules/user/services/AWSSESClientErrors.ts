import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { PossibleLngs } from '@shared/utils/utils';
import { Status } from '@shared/core/Status';

export namespace AWSSESClientErrors {
  export class EmailAddressBounced extends BaseError {
    public constructor(args: { lng: PossibleLngs }) {
      const { lng } = args;
      const t = trans[lng];
      super({
        message: t.emailAddressBounced,
        status: Status.BAD_REQUEST,
      });
    }
  }
  
  export class InvalidEmailAddress extends BaseError {
    public constructor(args: { lng: PossibleLngs }) {
      const { lng } = args;
      const t = trans[lng];
      super({
        message: t.invalidEmailAddress,
        status: Status.BAD_REQUEST,
      });
    }
  }
  
  export class InternalError extends BaseError {
    public constructor(args: { lng: PossibleLngs }) {
      const { lng } = args;
      const t = trans[lng];
      super({
        message: t.internalError,
        status: Status.INTERNAL_ERROR,
      });
    }
  }
}

const trans = {
  en: {
    emailAddressBounced: `This email address has bounced or is invalid. Please verify the email address and try again.`,
    invalidEmailAddress: `The email address format is invalid. Please use a valid email format (e.g., <EMAIL>).`,
    internalError: `Unable to send verification code. Please try again later.`,
  },
  es: {
    emailAddressBounced: `Esta dirección de correo electrónico ha rebotado o no es válida. Por favor, verifica la dirección de correo e inténtalo de nuevo.`,
    invalidEmailAddress: `El formato de la dirección de correo electrónico no es válido. Por favor, utiliza un formato de correo válido (ej., <EMAIL>).`,
    internalError: `No se pudo enviar el código de verificación. Por favor, inténtalo de nuevo más tarde.`,
  },
};

patch({ AWSSESClientErrors });
