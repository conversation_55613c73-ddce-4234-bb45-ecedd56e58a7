// Set USE_REAL_AWS=true and TEST_PHONE_NUMBER=<real phone +XXXXXXXXXXXXX> in .env file to run this test
import { test, expect, vi, beforeEach, describe } from 'vitest';
import { AWSSNSClient } from './AWSSNSClient';
import { AWSSNSClientErrors } from './AWSSNSClientErrors';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { pickLng } from '@shared/utils/test';

const lng = pickLng();

// Skip tests if USE_REAL_AWS is not set to true
const USE_REAL_AWS = process.env.USE_REAL_AWS === 'true';
if (!USE_REAL_AWS) {
  test.skip('Integration tests skipped - set USE_REAL_AWS=true to run', () => {
    console.log('Skipping AWS SNS integration tests. Set USE_REAL_AWS=true to run them.');
  });
} else {
  describe('AWSSNSClient Integration Tests', () => {
    let snsClient: AWSSNSClient;
    let contextProvider: ContextProvider;
    
    beforeEach(() => {
      vi.clearAllMocks();
      contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
      snsClient = new AWSSNSClient({ contextProvider });
      vi.spyOn(contextProvider, 'sendAnalytics').mockResolvedValue();
    });

    test('client initializes without errors', () => {
      expect(snsClient).toBeInstanceOf(AWSSNSClient);
    });

    test.only('sends a real SMS message', async () => {
      // IMPORTANT: This test will actually send an SMS and incur AWS charges
      // Use a valid phone number that can receive SMS
      const TEST_PHONE_NUMBER = process.env.TEST_PHONE_NUMBER;
      
      if (!TEST_PHONE_NUMBER)
        throw 'TEST_PHONE_NUMBER environment variable not set';

      const params = {
        msg: 'This is a test message from AWSSNSClient integration test',
        phoneNumber: TEST_PHONE_NUMBER,
        lng,
      };

      const result = await snsClient.send(params);
      
      // Check if the message was sent successfully
      expect(result.isSuccess).toBe(true);
    });

    test('handles invalid phone number with real AWS', async () => {
      const params = {
        msg: 'Test message',
        phoneNumber: 'clearly-invalid-phone',
        lng,
      };

      const result = await snsClient.send(params);
      
      // Should fail with invalid phone number error
      expect(result.isFailure).toBe(true);
      expect(result.errors?.[0]).toBeInstanceOf(AWSSNSClientErrors.InvalidPhoneNumber);
    });
  });
}