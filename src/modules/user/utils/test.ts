import { Name } from '../domain/Name';
import { Email } from '../domain/Email';
import {
  BusinessUser,
  BusinessUserDto,
  getPhoneVerificationNull,
} from '../domain/BusinessUser';
import { CustomerUser, CustomerUserDto } from '../domain/CustomerUser';
import { orNull, pickLng } from '@shared/utils/test';
import { createTimeZone } from '@shared/core/test';
import {
  ManuallyCreatedCustomer,
  ManuallyCreatedCustomerDto,
} from '../domain/ManuallyCreatedCustomer';
import { Notes } from '../domain/Notes';
import { expect } from 'vitest';
import { Phone } from '../domain/Phone';
import { UserLng } from '../domain/UserLng';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

export const createName = () =>
  Name.create(chance.string({ alpha: true, numeric: true, length: 15 }))
    .value as Name;

export const createEmail = () => Email.create(chance.email()).value as Email;

export const createNotes = () => Notes.create(chance.string({ alpha: true, numeric: true, length: 200 })).value as Notes;

export function createBusinessUser(userId?: string): {
  user: BusinessUser;
  dto: BusinessUserDto;
} {
  const props = {
    email: createEmail(),
    firstName: createName(),
    lastName: createName(),
    lng: UserLng.create({ value: pickLng() }).value,
    phone: orNull(chance.phone()),
    timezone: createTimeZone(),
  };

  const user = userId?
    BusinessUser.assemble({
      email: props.email.value,
      firstName: props.firstName.value,
      lastName: props.lastName.value,
      lng: props.lng.value,
      phone: props.phone? props.phone.value : null,
      timezone: props.timezone.value,
      id: userId,
      phoneVerification: getPhoneVerificationNull(),
    })
    : BusinessUser.create(props);

  return { user, dto: user.toDto() };
}

export function createCustomerUser(id?: string): {
  user: CustomerUser;
  dto: CustomerUserDto;
} {
  const props = {
    email: createEmail(),
    firstName: createName(),
    lastName: createName(),
    phone: orNull(chance.phone()),
    timezone: createTimeZone(),
  };

  let user = CustomerUser.create(props);

  if (id) {
    user = CustomerUser.assemble({
      ...user.toDto(),
      id,
    });
  }

  return { user, dto: user.toDto() };
}

export function createCustomerManually(_userId?: string) {
  const props = {
    firstName: createName(),
    lastName: createName(),
    notes: orNull(createNotes()),
    createdBy: _userId? _userId : chance.guid({ version: 4 }),
  };

  const customer = ManuallyCreatedCustomer.create(props).value;

  return { customer, dto: customer.toDto() };
}

export function createManuallyCreatedCustomer(): {
  user: ManuallyCreatedCustomer;
  dto: ManuallyCreatedCustomerDto;
} {
  const props = {
    notes: orNull(createNotes()),
    firstName: createName(),
    lastName: createName(),
    createdBy: chance.guid({ version: 4 }),
  };

  const user = ManuallyCreatedCustomer.create(props).value;

  return { user, dto: user.toDto() };
}

export function removeCustomerUser(id: string) {
  return models.CustomerUser.destroy({
    where: { id },
    force: true,
  });
}

export function removeManuallyCreatedCustomer(id: string) {
  return models.ManuallyCreatedCustomer.destroy({
    where: { id },
    force: true,
  });
}

export function removeBusinessUser(id: string) {
  return models.BusinessUser.destroy({
    where: { id },
    force: true,
  });
}

export function expectInAbout24hrs(time: number) {
  const expected = new Date().getTime() + 24 * 60 * 60 * 1000;  // expected time in 24hrs ahead
  expect(Math.abs(expected - time)).toBeLessThan(2 * 60 * 1000);  // difference within 2m
}

export function expectInAboutAMinute(time: number) {
  const expected = new Date().getTime() + 60 * 1000;  // expected time is 1m ahead
  const diff = Math.abs(expected - time);
  expect(diff).toBeLessThan(1000);  // difference within 1s
}

export function getPhone() {
  const phone = '+1' + chance.string({ alpha: false, numeric: true, length: 10 });
  return Phone.create({ value: phone }).value;
}
